/**
 * Wistron Money Transfer v2.0 - Enhanced with Security
 * نظام ويسترون المحسن مع الأمان
 */

console.log('🚀 Starting Wistron Money Transfer v2.0...');

// Load required modules
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();

// Configuration
const JWT_SECRET = 'wistron_jwt_secret_2024';
const JWT_EXPIRES_IN = '24h';
const BCRYPT_ROUNDS = 12;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} [${req.method}] ${req.path}`);
  next();
});

// In-memory database with hashed passwords
const database = {
  users: [
    {
      id: '1',
      email: '<EMAIL>',
      password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', // Admin@123456
      first_name: 'System',
      last_name: 'Administrator',
      role: 'super_admin',
      status: 'active',
      kyc_status: 'approved',
      is_email_verified: true,
      created_at: new Date().toISOString()
    },
    {
      id: '2',
      email: '<EMAIL>',
      password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', // Agent@123456
      first_name: 'Sample',
      last_name: 'Agent',
      role: 'agent',
      status: 'active',
      kyc_status: 'approved',
      is_email_verified: true,
      created_at: new Date().toISOString()
    }
  ],
  transfers: [
    {
      id: '1',
      reference_number: 'WMT12345678',
      sender_id: '1',
      sender_name: 'John Doe',
      recipient_name: 'Ahmed Ali',
      send_amount: 1000,
      send_currency: 'USD',
      receive_amount: 3750,
      receive_currency: 'SAR',
      exchange_rate: 3.75,
      fees: 15,
      status: 'completed',
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      reference_number: 'WMT87654321',
      sender_id: '2',
      sender_name: 'Sarah Johnson',
      recipient_name: 'Fatima Hassan',
      send_amount: 500,
      send_currency: 'USD',
      receive_amount: 1835,
      receive_currency: 'AED',
      exchange_rate: 3.67,
      fees: 8,
      status: 'processing',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    }
  ],
  sessions: [],
  rates: {
    SAR: 3.75, AED: 3.67, EGP: 30.85, EUR: 0.85, GBP: 0.73,
    JOD: 0.71, KWD: 0.31, QAR: 3.64, BHD: 0.38, OMR: 0.38
  }
};

// Utility functions
async function hashPassword(password) {
  return await bcrypt.hash(password, BCRYPT_ROUNDS);
}

async function verifyPassword(password, hash) {
  return await bcrypt.compare(password, hash);
}

function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

function verifyToken(token) {
  return jwt.verify(token, JWT_SECRET);
}

// Authentication middleware
function authenticate(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'مطلوب رمز المصادقة',
      message: 'Authentication token required'
    });
  }

  try {
    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: 'رمز المصادقة غير صالح',
      message: 'Invalid or expired token'
    });
  }
}

// Authorization middleware
function authorize(...roles) {
  return (req, res, next) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'غير مخول للوصول',
        message: 'Insufficient permissions'
      });
    }
    next();
  };
}

console.log('🛣️  Setting up enhanced routes...');

// Home page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>🏦 Wistron Money Transfer v2.0</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea, #764ba2); color: white; min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        h1 { font-size: 3.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .status { background: rgba(76,175,80,0.2); border: 2px solid #4CAF50; border-radius: 15px; padding: 25px; margin: 30px 0; text-align: center; }
        .security { background: rgba(255,193,7,0.2); border: 2px solid #FFC107; border-radius: 15px; padding: 20px; margin: 20px 0; text-align: center; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0; }
        .card { background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); }
        .card h3 { color: #FFD700; margin-bottom: 20px; }
        .stat { display: flex; justify-content: space-between; margin: 12px 0; padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); }
        .stat-value { font-weight: bold; color: #4CAF50; }
        .btn { display: inline-block; padding: 12px 25px; background: white; color: #667eea; text-decoration: none; border-radius: 8px; margin: 8px; font-weight: bold; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 20px rgba(0,0,0,0.2); }
        .endpoint { background: rgba(0,0,0,0.2); padding: 15px; margin: 10px 0; border-radius: 8px; font-family: monospace; }
        .method { color: #4CAF50; font-weight: bold; margin-right: 15px; }
        .feature { background: rgba(76,175,80,0.1); padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #4CAF50; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏦 Wistron Money Transfer</h1>
          <p style="font-size: 1.3em;">نظام ويسترون المحسن v2.0 - Enhanced Security System</p>
        </div>
        
        <div class="status">
          <h2>🚀 النظام المحسن v2.0 يعمل بنجاح!</h2>
          <p><strong>✅ ENHANCED SYSTEM v2.0: FULLY OPERATIONAL</strong></p>
          <p>JWT Authentication + bcrypt + Enhanced Security</p>
          <p>Server Uptime: ${Math.floor(process.uptime())} seconds</p>
        </div>
        
        <div class="security">
          <h3>🔒 Enhanced Security Features v2.0</h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <div class="feature">🔐 JWT Authentication</div>
            <div class="feature">🛡️ bcrypt Password Hashing</div>
            <div class="feature">🎯 Role-based Authorization</div>
            <div class="feature">🔒 Protected Routes</div>
            <div class="feature">📝 Session Management</div>
            <div class="feature">⚡ Enhanced Validation</div>
          </div>
        </div>
        
        <div class="grid">
          <div class="card">
            <h3>📊 System Statistics</h3>
            <div class="stat"><span>Total Users:</span><span class="stat-value">${database.users.length}</span></div>
            <div class="stat"><span>Active Users:</span><span class="stat-value">${database.users.filter(u => u.status === 'active').length}</span></div>
            <div class="stat"><span>Total Transfers:</span><span class="stat-value">${database.transfers.length}</span></div>
            <div class="stat"><span>Completed:</span><span class="stat-value">${database.transfers.filter(t => t.status === 'completed').length}</span></div>
            <div class="stat"><span>Total Volume:</span><span class="stat-value">$${database.transfers.reduce((sum, t) => sum + t.send_amount, 0).toLocaleString()}</span></div>
            <div class="stat"><span>Active Sessions:</span><span class="stat-value">${database.sessions.length}</span></div>
          </div>
          
          <div class="card">
            <h3>👤 Test Accounts</h3>
            <p><strong>Super Administrator:</strong><br>📧 <EMAIL><br>🔑 Admin@123456</p>
            <p><strong>System Agent:</strong><br>📧 <EMAIL><br>🔑 Agent@123456</p>
            <div style="margin-top: 20px;">
              <a href="/health" class="btn">🏥 Health Check</a>
              <a href="/api-docs" class="btn">📚 API Docs</a>
            </div>
          </div>
          
          <div class="card">
            <h3>💱 Exchange Rates</h3>
            <div class="stat"><span>USD → SAR:</span><span class="stat-value">${database.rates.SAR}</span></div>
            <div class="stat"><span>USD → AED:</span><span class="stat-value">${database.rates.AED}</span></div>
            <div class="stat"><span>USD → EGP:</span><span class="stat-value">${database.rates.EGP}</span></div>
            <div class="stat"><span>USD → EUR:</span><span class="stat-value">${database.rates.EUR}</span></div>
            <div style="margin-top: 15px;">
              <a href="/api/v1/rates" class="btn">💱 Get Rates</a>
            </div>
          </div>
          
          <div class="card">
            <h3>🔧 Enhanced API Endpoints</h3>
            <div class="endpoint"><span class="method">GET</span>/health - Enhanced Health Check</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/auth/login - JWT Authentication</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/auth/register - User Registration</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/auth/logout - Secure Logout</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/rates - Exchange Rates</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/dashboard/stats - Protected Stats</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/transfers - Protected Transfers</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/transfers - Create Transfer (Auth)</div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Enhanced health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    message: 'Wistron Money Transfer v2.0 - Enhanced System Operational',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '2.0.0-enhanced',
    security: {
      jwtEnabled: true,
      bcryptEnabled: true,
      authenticationActive: true,
      protectedRoutesActive: true
    },
    stats: {
      totalUsers: database.users.length,
      activeSessions: database.sessions.length,
      totalTransfers: database.transfers.length,
      completedTransfers: database.transfers.filter(t => t.status === 'completed').length
    }
  });
});

// Authentication routes
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'البريد الإلكتروني وكلمة المرور مطلوبان',
        message: 'Email and password are required'
      });
    }

    console.log(`🔐 Login attempt: ${email}`);

    // Find user
    const user = database.users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'بيانات الدخول غير صحيحة',
        message: 'Invalid credentials'
      });
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'بيانات الدخول غير صحيحة',
        message: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    // Create session
    const session = {
      id: String(database.sessions.length + 1),
      user_id: user.id,
      token: token,
      created_at: new Date().toISOString(),
      is_active: true
    };
    database.sessions.push(session);

    console.log(`✅ Login successful: ${email} (${user.role})`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح - Login Successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          status: user.status,
          kycStatus: user.kyc_status,
          isEmailVerified: user.is_email_verified
        },
        tokens: {
          accessToken: token,
          expiresIn: JWT_EXPIRES_IN
        }
      }
    });

  } catch (error) {
    console.error('Login error:', error.message);
    res.status(500).json({
      success: false,
      error: 'خطأ في تسجيل الدخول',
      message: 'Login failed'
    });
  }
});

app.post('/api/v1/auth/register', async (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;

    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: 'جميع الحقول مطلوبة',
        message: 'All fields are required'
      });
    }

    console.log(`📝 Registration attempt: ${email}`);

    // Check if user exists
    const existingUser = database.users.find(u => u.email === email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'البريد الإلكتروني مستخدم بالفعل',
        message: 'Email already exists'
      });
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const newUser = {
      id: String(database.users.length + 1),
      email,
      password_hash: passwordHash,
      first_name: firstName,
      last_name: lastName,
      role: 'customer',
      status: 'active',
      kyc_status: 'not_started',
      is_email_verified: false,
      created_at: new Date().toISOString()
    };

    database.users.push(newUser);

    console.log(`✅ User registered: ${email}`);

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الحساب بنجاح - Account Created Successfully',
      data: {
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.first_name,
          lastName: newUser.last_name,
          status: newUser.status
        }
      }
    });

  } catch (error) {
    console.error('Registration error:', error.message);
    res.status(500).json({
      success: false,
      error: 'فشل إنشاء الحساب',
      message: 'Registration failed'
    });
  }
});

app.post('/api/v1/auth/logout', authenticate, (req, res) => {
  try {
    // Deactivate session
    const session = database.sessions.find(s => s.user_id === req.user.userId && s.is_active);
    if (session) {
      session.is_active = false;
    }

    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح - Logout Successful'
    });

  } catch (error) {
    console.error('Logout error:', error.message);
    res.status(500).json({
      success: false,
      error: 'فشل تسجيل الخروج',
      message: 'Logout failed'
    });
  }
});

// Exchange rates (public)
app.get('/api/v1/rates', (req, res) => {
  res.json({
    success: true,
    message: 'أسعار الصرف الحالية - Current Exchange Rates',
    data: {
      base: 'USD',
      rates: database.rates,
      lastUpdated: new Date().toISOString()
    }
  });
});

// Protected dashboard stats
app.get('/api/v1/dashboard/stats', authenticate, authorize('admin', 'super_admin', 'agent'), (req, res) => {
  const stats = {
    totalUsers: database.users.length,
    activeUsers: database.users.filter(u => u.status === 'active').length,
    totalTransfers: database.transfers.length,
    completedTransfers: database.transfers.filter(t => t.status === 'completed').length,
    totalVolume: database.transfers.reduce((sum, t) => sum + t.send_amount, 0),
    activeSessions: database.sessions.filter(s => s.is_active).length
  };

  res.json({
    success: true,
    message: 'إحصائيات النظام المحمية - Protected System Statistics',
    data: {
      ...stats,
      averageTransferAmount: stats.totalVolume / stats.totalTransfers || 0,
      serverUptime: Math.floor(process.uptime()),
      requestedBy: req.user.email,
      requestedAt: new Date().toISOString()
    }
  });
});

// Protected transfers list
app.get('/api/v1/transfers', authenticate, (req, res) => {
  let transfers = [...database.transfers];

  // Filter by user role
  if (req.user.role === 'customer') {
    transfers = transfers.filter(t => t.sender_id === req.user.userId);
  }

  res.json({
    success: true,
    message: 'قائمة التحويلات المحمية - Protected Transfers List',
    data: {
      transfers,
      totalCount: transfers.length,
      requestedBy: req.user.email
    }
  });
});

// Protected transfer creation
app.post('/api/v1/transfers', authenticate, async (req, res) => {
  try {
    const { senderName, recipientName, sendAmount, sendCurrency, receiveCurrency, deliveryMethod } = req.body;

    if (!senderName || !recipientName || !sendAmount || !sendCurrency || !receiveCurrency) {
      return res.status(400).json({
        success: false,
        error: 'جميع الحقول مطلوبة',
        message: 'All fields are required'
      });
    }

    // Calculate transfer details
    const exchangeRate = database.rates[receiveCurrency] || 1;
    const receiveAmount = Math.round(sendAmount * exchangeRate * 100) / 100;
    const fees = Math.max(5, Math.round(sendAmount * 0.015 * 100) / 100);

    // Generate reference number
    const referenceNumber = 'WMT' + Math.random().toString(36).substr(2, 8).toUpperCase();

    const newTransfer = {
      id: String(database.transfers.length + 1),
      reference_number: referenceNumber,
      sender_id: req.user.userId,
      sender_name: senderName,
      recipient_name: recipientName,
      send_amount: parseFloat(sendAmount),
      send_currency: sendCurrency,
      receive_amount: receiveAmount,
      receive_currency: receiveCurrency,
      exchange_rate: exchangeRate,
      fees: fees,
      delivery_method: deliveryMethod || 'cash_pickup',
      status: 'pending',
      created_at: new Date().toISOString()
    };

    database.transfers.push(newTransfer);

    console.log(`💸 New transfer created: ${referenceNumber} by ${req.user.email}`);

    res.status(201).json({
      success: true,
      message: 'تم إنشاء التحويل بنجاح - Transfer Created Successfully',
      data: {
        transfer: newTransfer,
        createdBy: req.user.email
      }
    });

  } catch (error) {
    console.error('Transfer creation error:', error.message);
    res.status(500).json({
      success: false,
      error: 'فشل إنشاء التحويل',
      message: 'Transfer creation failed'
    });
  }
});

// Protected users list (admin only)
app.get('/api/v1/users', authenticate, authorize('admin', 'super_admin'), (req, res) => {
  const users = database.users.map(u => ({
    id: u.id,
    email: u.email,
    firstName: u.first_name,
    lastName: u.last_name,
    role: u.role,
    status: u.status,
    kycStatus: u.kyc_status,
    createdAt: u.created_at
  }));

  res.json({
    success: true,
    message: 'قائمة المستخدمين المحمية - Protected Users List',
    data: {
      users,
      totalCount: users.length,
      requestedBy: req.user.email
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'المسار غير موجود - Route Not Found',
    message: `المسار ${req.originalUrl} غير موجود`,
    availableRoutes: [
      'GET /', 'GET /health',
      'POST /api/v1/auth/login', 'POST /api/v1/auth/register', 'POST /api/v1/auth/logout',
      'GET /api/v1/rates', 'GET /api/v1/dashboard/stats (protected)',
      'GET /api/v1/transfers (protected)', 'POST /api/v1/transfers (protected)',
      'GET /api/v1/users (admin only)'
    ]
  });
});

// Start server
const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`
🏦 ========================================
   WISTRON MONEY TRANSFER v2.0 - ENHANCED
   نظام ويسترون المحسن الإصدار 2.0
========================================

🚀 Enhanced System v2.0: http://localhost:${PORT}
🏥 Health Check: http://localhost:${PORT}/health
📊 Dashboard: http://localhost:${PORT}

🔒 Security Features v2.0:
   ✅ JWT Authentication
   ✅ bcrypt Password Hashing
   ✅ Role-based Authorization
   ✅ Protected Routes
   ✅ Session Management

👤 Test Accounts:
   Super Admin: <EMAIL> / Admin@123456
   System Agent: <EMAIL> / Agent@123456

📊 System Stats:
   Users: ${database.users.length}
   Transfers: ${database.transfers.length}
   Sessions: ${database.sessions.length}

🔧 Enhanced APIs:
   Authentication, Protected Routes, JWT Security

✅ WISTRON v2.0 ENHANCED SYSTEM OPERATIONAL!

========================================`);
});

module.exports = { app, database };
