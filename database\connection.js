/**
 * Database Connection Manager
 * مدير اتصال قاعدة البيانات
 */

const knex = require('knex');
const config = require('./config');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      const environment = process.env.NODE_ENV || 'development';
      console.log(`🔌 Connecting to database (${environment})...`);
      
      this.db = knex(config[environment]);
      
      // Test connection
      await this.db.raw('SELECT 1');
      
      this.isConnected = true;
      console.log('✅ Database connected successfully');
      
      return this.db;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      
      // Fallback to in-memory storage for development
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Falling back to in-memory storage...');
        return this.createInMemoryFallback();
      }
      
      throw error;
    }
  }

  createInMemoryFallback() {
    console.log('📝 Creating in-memory database fallback...');
    
    // In-memory storage as fallback
    const inMemoryDB = {
      users: [
        {
          id: '1',
          email: '<EMAIL>',
          password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', // Admin@123456
          first_name: 'System',
          last_name: 'Administrator',
          role: 'super_admin',
          status: 'active',
          kyc_status: 'approved',
          is_email_verified: true,
          is_phone_verified: false,
          is_2fa_enabled: false,
          preferred_language: 'en',
          preferred_currency: 'USD',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          email: '<EMAIL>',
          password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', // Agent@123456
          first_name: 'Sample',
          last_name: 'Agent',
          role: 'agent',
          status: 'active',
          kyc_status: 'approved',
          is_email_verified: true,
          is_phone_verified: false,
          is_2fa_enabled: false,
          preferred_language: 'en',
          preferred_currency: 'USD',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      transfers: [
        {
          id: '1',
          reference_number: 'WMT12345678',
          tracking_number: '123456789012',
          sender_id: '1',
          sender_name: 'John Doe',
          sender_email: '<EMAIL>',
          recipient_name: 'Ahmed Ali',
          recipient_phone: '+966501234567',
          send_amount: 1000,
          send_currency: 'USD',
          receive_amount: 3750,
          receive_currency: 'SAR',
          exchange_rate: 3.75,
          fees: 15,
          total_amount: 1015,
          delivery_method: 'cash_pickup',
          status: 'completed',
          payment_status: 'paid',
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          reference_number: 'WMT87654321',
          tracking_number: '************',
          sender_id: '2',
          sender_name: 'Sarah Johnson',
          sender_email: '<EMAIL>',
          recipient_name: 'Fatima Hassan',
          recipient_phone: '+************',
          send_amount: 500,
          send_currency: 'USD',
          receive_amount: 1835,
          receive_currency: 'AED',
          exchange_rate: 3.67,
          fees: 8,
          total_amount: 508,
          delivery_method: 'bank_deposit',
          status: 'processing',
          payment_status: 'paid',
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        }
      ],
      user_sessions: [],
      notifications: [],
      exchange_rates: {
        base: 'USD',
        rates: {
          SAR: 3.75,
          AED: 3.67,
          EGP: 30.85,
          JOD: 0.71,
          KWD: 0.31,
          QAR: 3.64,
          BHD: 0.38,
          OMR: 0.38,
          EUR: 0.85,
          GBP: 0.73,
          CAD: 1.35,
          AUD: 1.52,
          JPY: 150.25,
          CHF: 0.88
        },
        last_updated: new Date().toISOString()
      }
    };

    // Create mock database interface
    const mockDB = {
      // Mock query methods
      select: (table) => ({
        from: () => Promise.resolve(inMemoryDB[table] || []),
        where: (conditions) => Promise.resolve(inMemoryDB[table]?.filter(item => {
          return Object.keys(conditions).every(key => item[key] === conditions[key]);
        }) || []),
        first: () => Promise.resolve(inMemoryDB[table]?.[0] || null)
      }),
      
      insert: (table, data) => {
        if (!inMemoryDB[table]) inMemoryDB[table] = [];
        const newItem = { 
          id: String(inMemoryDB[table].length + 1), 
          ...data,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        inMemoryDB[table].push(newItem);
        return Promise.resolve([newItem]);
      },
      
      update: (table, data) => ({
        where: (conditions) => {
          const items = inMemoryDB[table] || [];
          items.forEach(item => {
            if (Object.keys(conditions).every(key => item[key] === conditions[key])) {
              Object.assign(item, data, { updated_at: new Date().toISOString() });
            }
          });
          return Promise.resolve(1);
        }
      }),
      
      raw: (query) => Promise.resolve({ rows: [{ result: 'OK' }] }),
      
      // Table access
      from: (table) => mockDB.select(table),
      
      // Direct table access
      users: inMemoryDB.users,
      transfers: inMemoryDB.transfers,
      user_sessions: inMemoryDB.user_sessions,
      notifications: inMemoryDB.notifications,
      
      // Utility methods
      isInMemory: true,
      getData: () => inMemoryDB
    };

    this.db = mockDB;
    this.isConnected = true;
    
    console.log('✅ In-memory database fallback ready');
    return mockDB;
  }

  async disconnect() {
    if (this.db && !this.db.isInMemory) {
      await this.db.destroy();
    }
    this.isConnected = false;
    console.log('🔌 Database disconnected');
  }

  getConnection() {
    if (!this.isConnected) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  async runMigrations() {
    if (this.db.isInMemory) {
      console.log('📝 Skipping migrations for in-memory database');
      return;
    }

    try {
      console.log('🔄 Running database migrations...');
      await this.db.migrate.latest();
      console.log('✅ Migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      throw error;
    }
  }

  async seedDatabase() {
    if (this.db.isInMemory) {
      console.log('📝 Skipping seeds for in-memory database (already seeded)');
      return;
    }

    try {
      console.log('🌱 Running database seeds...');
      await this.db.seed.run();
      console.log('✅ Seeds completed successfully');
    } catch (error) {
      console.error('❌ Seeding failed:', error.message);
      throw error;
    }
  }
}

// Create singleton instance
const dbManager = new DatabaseManager();

module.exports = dbManager;
