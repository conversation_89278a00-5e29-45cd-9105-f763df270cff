#!/usr/bin/env node
const rechoir = require('rechoir');
const merge = require('lodash/merge');
const interpret = require('interpret');
const resolveFrom = require('resolve-from');
const path = require('path');
const tildify = require('tildify');
const commander = require('commander');
const color = require('colorette');
const argv = require('getopts')(process.argv.slice(2));
const cliPkg = require('../package');
const {
  parseConfigObj,
  mkConfigObj,
  resolveEnvironmentConfig,
  exit,
  success,
  checkLocalModule,
  checkConfigurationOptions,
  getMigrationExtension,
  getSeedExtension,
  getStubPath,
  findUpModulePath,
  findUpConfig,
} = require('./utils/cli-config-utils');
const {
  existsSync,
  readFile,
  writeFile,
} = require('../lib/migrations/util/fs');

const { listMigrations } = require('./utils/migrationsLister');

async function openKnexfile(configPath) {
  const importFile = require('../lib/migrations/util/import-file'); // require me late!
  let config = await importFile(configPath);
  if (config && config.default) {
    config = config.default;
  }
  if (typeof config === 'function') {
    config = await config();
  }
  return config;
}

async function initKnex(env, opts, useDefaultClientIfNotSpecified) {
  checkLocalModule(env);
  if (process.cwd() !== env.cwd) {
    process.chdir(env.cwd);
    console.log(
      'Working directory changed to',
      color.magenta(tildify(env.cwd))
    );
  }

  if (!useDefaultClientIfNotSpecified) {
    checkConfigurationOptions(env, opts);
  }

  env.configuration = env.configPath
    ? await openKnexfile(env.configPath)
    : mkConfigObj(opts);

  const resolvedConfig = resolveEnvironmentConfig(
    opts,
    env.configuration,
    env.configPath
  );

  const optionsConfig = parseConfigObj(opts);
  const config = merge(resolvedConfig, optionsConfig);

  // Migrations directory gets defaulted if it is undefined.
  if (!env.configPath && !config.migrations.directory) {
    config.migrations.directory = null;
  }

  // Client gets defaulted if undefined and it's allowed
  if (useDefaultClientIfNotSpecified && config.client === undefined) {
    config.client = 'sqlite3';
  }

  const knex = require(env.modulePath);
  return knex(config);
}

function invoke() {
  const filetypes = ['js', 'mjs', 'coffee', 'ts', 'eg', 'ls'];

  const cwd = argv.knexfile
    ? path.dirname(path.resolve(argv.knexfile))
    : process.cwd();

  // TODO add knexpath here eventually
  const modulePath =
    resolveFrom.silent(cwd, 'knex') ||
    findUpModulePath(cwd, 'knex') ||
    process.env.KNEX_PATH;

  const configPath =
    argv.knexfile && existsSync(argv.knexfile)
      ? path.resolve(argv.knexfile)
      : findUpConfig(cwd, 'knexfile', filetypes);

  if (configPath) {
    const autoloads = rechoir.prepare(
      interpret.jsVariants,
      configPath,
      cwd,
      true
    );
    if (autoloads instanceof Error) {
      // Only errors
      autoloads.failures.forEach(function (failed) {
        console.log(
          color.red('Failed to load external module'),
          color.magenta(failed.moduleName)
        );
      });
    } else if (Array.isArray(autoloads)) {
      const succeeded = autoloads[autoloads.length - 1];
      console.log(
        'Requiring external module',
        color.magenta(succeeded.moduleName)
      );
    }
  }

  const env = {
    cwd,
    modulePath,
    configPath,
    configuration: null,
  };

  let modulePackage = {};
  try {
    modulePackage = require(path.join(
      path.dirname(env.modulePath),
      'package.json'
    ));
  } catch (e) {
    /* empty */
  }

  const cliVersion = [
    color.blue('Knex CLI version:'),
    color.green(cliPkg.version),
  ].join(' ');

  const localVersion = [
    color.blue('Knex Local version:'),
    color.green(modulePackage.version || 'None'),
  ].join(' ');

  commander
    .version(`${cliVersion}\n${localVersion}`)
    .option('--debug', 'Run with debugging.')
    .option('--knexfile [path]', 'Specify the knexfile path.')
    .option('--knexpath [path]', 'Specify the path to knex instance.')
    .option(