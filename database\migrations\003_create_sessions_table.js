/**
 * Create Sessions Table Migration
 * إنشاء جدول الجلسات
 */

exports.up = function(knex) {
  return knex.schema.createTable('user_sessions', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // User reference
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Session information
    table.string('token_hash').notNullable().unique();
    table.string('refresh_token_hash').nullable();
    table.enum('token_type', ['access', 'refresh', 'reset_password', 'email_verification']).defaultTo('access');
    
    // Device and location information
    table.string('device_id').nullable();
    table.string('device_name').nullable();
    table.string('device_type').nullable(); // mobile, desktop, tablet
    table.string('browser').nullable();
    table.string('os').nullable();
    table.string('ip_address').nullable();
    table.string('user_agent').nullable();
    table.string('location').nullable(); // City, Country
    
    // Session status
    table.boolean('is_active').defaultTo(true);
    table.timestamp('expires_at').notNullable();
    table.timestamp('last_used_at').defaultTo(knex.fn.now());
    
    // Security flags
    table.boolean('is_suspicious').defaultTo(false);
    table.text('security_notes').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['token_hash']);
    table.index(['expires_at']);
    table.index(['is_active']);
    table.index(['created_at']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('user_sessions');
};
