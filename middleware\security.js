/**
 * Security Middleware
 * وسطاء الأمان
 */

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { body, validationResult } = require('express-validator');
const authService = require('../services/auth');

/**
 * Rate limiting configuration
 * إعدادات تحديد المعدل
 */
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      error: 'Too many requests',
      message: message || 'Too many requests from this IP, please try again later',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      console.log(`🚫 Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        success: false,
        error: 'معدل الطلبات مرتفع جداً',
        message: 'Too many requests from this IP, please try again later',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// Different rate limits for different endpoints
const rateLimits = {
  // General API rate limit
  general: createRateLimit(
    15 * 60 * 1000, // 15 minutes
    100, // 100 requests per window
    'Too many requests, please try again later'
  ),

  // Strict rate limit for authentication
  auth: createRateLimit(
    15 * 60 * 1000, // 15 minutes
    5, // 5 login attempts per window
    'Too many login attempts, please try again later'
  ),

  // Rate limit for password reset
  passwordReset: createRateLimit(
    60 * 60 * 1000, // 1 hour
    3, // 3 password reset attempts per hour
    'Too many password reset attempts, please try again later'
  ),

  // Rate limit for registration
  registration: createRateLimit(
    60 * 60 * 1000, // 1 hour
    3, // 3 registration attempts per hour
    'Too many registration attempts, please try again later'
  )
};

/**
 * Helmet security configuration
 * إعدادات أمان Helmet
 */
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * Authentication middleware
 * وسطاء المصادقة
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'مطلوب رمز المصادقة',
        message: 'Authentication token required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Validate session
    const decoded = await authService.validateSession(token);
    
    // Add user info to request
    req.user = decoded;
    req.token = token;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error.message);
    return res.status(401).json({
      success: false,
      error: 'رمز المصادقة غير صالح',
      message: 'Invalid or expired authentication token'
    });
  }
};

/**
 * Authorization middleware (role-based)
 * وسطاء التخويل (حسب الدور)
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'مطلوب مصادقة',
        message: 'Authentication required'
      });
    }

    if (!roles.includes(req.user.role)) {
      console.log(`🚫 Access denied for user ${req.user.email} with role ${req.user.role}`);
      return res.status(403).json({
        success: false,
        error: 'غير مخول للوصول',
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

/**
 * Input validation middleware
 * وسطاء التحقق من المدخلات
 */
const validateInput = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      error: 'بيانات غير صالحة',
      message: 'Validation failed',
      details: errorMessages
    });
  }

  next();
};

/**
 * Login validation rules
 * قواعد التحقق من تسجيل الدخول
 */
const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

/**
 * Registration validation rules
 * قواعد التحقق من التسجيل
 */
const registrationValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Valid phone number is required'),
  body('country')
    .optional()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country code must be 2 characters')
];

/**
 * Transfer validation rules
 * قواعد التحقق من التحويل
 */
const transferValidation = [
  body('senderName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Sender name must be between 2 and 100 characters'),
  body('recipientName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Recipient name must be between 2 and 100 characters'),
  body('sendAmount')
    .isFloat({ min: 1, max: 50000 })
    .withMessage('Send amount must be between 1 and 50,000'),
  body('sendCurrency')
    .isIn(['USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP', 'JOD', 'KWD', 'QAR', 'BHD', 'OMR'])
    .withMessage('Invalid send currency'),
  body('receiveCurrency')
    .isIn(['USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP', 'JOD', 'KWD', 'QAR', 'BHD', 'OMR'])
    .withMessage('Invalid receive currency'),
  body('deliveryMethod')
    .isIn(['cash_pickup', 'bank_deposit', 'mobile_wallet', 'home_delivery'])
    .withMessage('Invalid delivery method')
];

/**
 * Device info extractor
 * مستخرج معلومات الجهاز
 */
const extractDeviceInfo = (req, res, next) => {
  const userAgent = req.headers['user-agent'] || '';
  const ip = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  
  req.deviceInfo = {
    ip,
    userAgent,
    deviceId: req.headers['x-device-id'],
    deviceName: req.headers['x-device-name'],
    deviceType: req.headers['x-device-type'] || 'unknown',
    browser: extractBrowser(userAgent),
    os: extractOS(userAgent),
    location: req.headers['x-user-location']
  };
  
  next();
};

/**
 * Extract browser from user agent
 * استخراج المتصفح من وكيل المستخدم
 */
function extractBrowser(userAgent) {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  return 'Unknown';
}

/**
 * Extract OS from user agent
 * استخراج نظام التشغيل من وكيل المستخدم
 */
function extractOS(userAgent) {
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Mac')) return 'macOS';
  if (userAgent.includes('Linux')) return 'Linux';
  if (userAgent.includes('Android')) return 'Android';
  if (userAgent.includes('iOS')) return 'iOS';
  return 'Unknown';
}

module.exports = {
  rateLimits,
  helmetConfig,
  authenticate,
  authorize,
  validateInput,
  loginValidation,
  registrationValidation,
  transferValidation,
  extractDeviceInfo
};
