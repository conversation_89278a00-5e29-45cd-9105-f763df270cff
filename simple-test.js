// Simple test for Wistron v2.0
const http = require('http');

console.log('🧪 Simple Test - Wistron v2.0\n');

function simpleTest() {
  const req = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET',
    timeout: 5000
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log(`✅ Server Response: ${res.statusCode}`);
      try {
        const response = JSON.parse(data);
        console.log(`✅ Status: ${response.status}`);
        console.log(`✅ Version: ${response.version}`);
        console.log(`✅ JWT Enabled: ${response.security.jwtEnabled}`);
        console.log(`✅ bcrypt Enabled: ${response.security.bcryptEnabled}`);
        console.log(`✅ Users: ${response.stats.totalUsers}`);
        console.log(`✅ Transfers: ${response.stats.totalTransfers}`);
        
        console.log('\n🎉 ========================================');
        console.log('   WISTRON v2.0 ENHANCED SYSTEM RUNNING!');
        console.log('   نظام ويسترون المحسن v2.0 يعمل!');
        console.log('========================================');
        console.log('🌐 Access: http://localhost:5000');
        console.log('🔒 Security: JWT + bcrypt + Protected Routes');
        console.log('👤 Admin: <EMAIL> / Admin@123456');
        console.log('👤 Agent: <EMAIL> / Agent@123456');
        console.log('========================================');
        
      } catch (e) {
        console.log(`Response: ${data}`);
      }
    });
  });

  req.on('error', (err) => {
    console.log(`❌ Connection failed: ${err.message}`);
    console.log('Please make sure the server is running: node wistron-v2.js');
  });

  req.on('timeout', () => {
    console.log('❌ Request timeout');
    req.destroy();
  });

  req.end();
}

// Test immediately
simpleTest();
