-- Wistron Money Transfer Database Setup for MySQL
-- إعداد قاعدة بيانات ويسترون لتحويل الأموال - MySQL

-- Create database
CREATE DATABASE IF NOT EXISTS `wistron_money_transfer` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `wistron_money_transfer`;

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` VARCHAR(36) NOT NULL DEFAULT (UUID()),
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `password_hash` VARCHAR(255) NOT NULL,
  `first_name` VA<PERSON>HA<PERSON>(255) NOT NULL,
  `last_name` VA<PERSON>HA<PERSON>(255) NOT NULL,
  `phone` VARCHAR(255) UNIQUE,
  `date_of_birth` DATE,
  `gender` ENUM('male', 'female', 'other'),
  `address_line1` VARCHAR(255),
  `address_line2` VARCHAR(255),
  `city` VARCHAR(255),
  `state` VARCHAR(255),
  `postal_code` VARCHAR(255),
  `country` VARCHAR(2),
  `role` ENUM('customer', 'agent', 'admin', 'super_admin') DEFAULT 'customer',
  `status` ENUM('pending', 'active', 'suspended', 'closed') DEFAULT 'pending',
  `kyc_status` ENUM('not_started', 'pending', 'approved', 'rejected') DEFAULT 'not_started',
  `is_2fa_enabled` BOOLEAN DEFAULT FALSE,
  `is_email_verified` BOOLEAN DEFAULT FALSE,
  `is_phone_verified` BOOLEAN DEFAULT FALSE,
  `email_verification_token` VARCHAR(255),
  `phone_verification_token` VARCHAR(255),
  `email_verified_at` TIMESTAMP NULL,
  `phone_verified_at` TIMESTAMP NULL,
  `last_login` TIMESTAMP NULL,
  `last_login_ip` VARCHAR(255),
  `failed_login_attempts` INT DEFAULT 0,
  `locked_until` TIMESTAMP NULL,
  `preferred_language` VARCHAR(2) DEFAULT 'en',
  `preferred_currency` VARCHAR(3) DEFAULT 'USD',
  `timezone` VARCHAR(255) DEFAULT 'UTC',
  `metadata` JSON DEFAULT ('{}'),
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_users_email` (`email`),
  INDEX `idx_users_phone` (`phone`),
  INDEX `idx_users_status` (`status`),
  INDEX `idx_users_kyc_status` (`kyc_status`),
  INDEX `idx_users_role` (`role`),
  INDEX `idx_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create transfers table
CREATE TABLE IF NOT EXISTS `transfers` (
  `id` VARCHAR(36) NOT NULL DEFAULT (UUID()),
  `reference_number` VARCHAR(255) NOT NULL UNIQUE,
  `tracking_number` VARCHAR(255) NOT NULL UNIQUE,
  `sender_id` VARCHAR(36),
  `sender_name` VARCHAR(255) NOT NULL,
  `sender_phone` VARCHAR(255),
  `sender_email` VARCHAR(255),
  `sender_address` VARCHAR(255),
  `sender_city` VARCHAR(255),
  `sender_country` VARCHAR(2),
  `recipient_name` VARCHAR(255) NOT NULL,
  `recipient_phone` VARCHAR(255),
  `recipient_email` VARCHAR(255),
  `recipient_address` VARCHAR(255),
  `recipient_city` VARCHAR(255),
  `recipient_country` VARCHAR(2) NOT NULL,
  `recipient_id_type` VARCHAR(255),
  `recipient_id_number` VARCHAR(255),
  `send_amount` DECIMAL(15,2) NOT NULL,
  `send_currency` VARCHAR(3) NOT NULL,
  `receive_amount` DECIMAL(15,2) NOT NULL,
  `receive_currency` VARCHAR(3) NOT NULL,
  `exchange_rate` DECIMAL(10,6) NOT NULL,
  `fees` DECIMAL(15,2) NOT NULL,
  `total_amount` DECIMAL(15,2) NOT NULL,
  `delivery_method` ENUM('cash_pickup', 'bank_deposit', 'mobile_wallet', 'home_delivery') NOT NULL,
  `bank_name` VARCHAR(255),
  `bank_code` VARCHAR(255),
  `account_number` VARCHAR(255),
  `account_holder_name` VARCHAR(255),
  `swift_code` VARCHAR(255),
  `iban` VARCHAR(255),
  `wallet_provider` VARCHAR(255),
  `wallet_number` VARCHAR(255),
  `pickup_agent_id` VARCHAR(36),
  `pickup_location` VARCHAR(255),
  `pickup_address` VARCHAR(255),
  `status` ENUM('pending', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'refunded', 'failed') DEFAULT 'pending',
  `payment_status` ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
  `paid_at` TIMESTAMP NULL,
  `processed_at` TIMESTAMP NULL,
  `completed_at` TIMESTAMP NULL,
  `cancelled_at` TIMESTAMP NULL,
  `expires_at` TIMESTAMP NULL,
  `purpose_of_transfer` VARCHAR(255),
  `transfer_reason` TEXT,
  `is_suspicious` BOOLEAN DEFAULT FALSE,
  `compliance_notes` TEXT,
  `processing_agent_id` VARCHAR(36),
  `completing_agent_id` VARCHAR(36),
  `payment_method` VARCHAR(255),
  `payment_reference` VARCHAR(255),
  `payment_details` JSON DEFAULT ('{}'),
  `metadata` JSON DEFAULT ('{}'),
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_transfers_reference_number` (`reference_number`),
  INDEX `idx_transfers_tracking_number` (`tracking_number`),
  INDEX `idx_transfers_sender_id` (`sender_id`),
  INDEX `idx_transfers_status` (`status`),
  INDEX `idx_transfers_payment_status` (`payment_status`),
  INDEX `idx_transfers_delivery_method` (`delivery_method`),
  INDEX `idx_transfers_recipient_country` (`recipient_country`),
  INDEX `idx_transfers_created_at` (`created_at`),
  INDEX `idx_transfers_expires_at` (`expires_at`),
  FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`pickup_agent_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`processing_agent_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`completing_agent_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_sessions table
CREATE TABLE IF NOT EXISTS `user_sessions` (
  `id` VARCHAR(36) NOT NULL DEFAULT (UUID()),
  `user_id` VARCHAR(36) NOT NULL,
  `token_hash` VARCHAR(255) NOT NULL UNIQUE,
  `refresh_token_hash` VARCHAR(255),
  `token_type` ENUM('access', 'refresh', 'reset_password', 'email_verification') DEFAULT 'access',
  `device_id` VARCHAR(255),
  `device_name` VARCHAR(255),
  `device_type` VARCHAR(255),
  `browser` VARCHAR(255),
  `os` VARCHAR(255),
  `ip_address` VARCHAR(255),
  `user_agent` VARCHAR(255),
  `location` VARCHAR(255),
  `is_active` BOOLEAN DEFAULT TRUE,
  `expires_at` TIMESTAMP NOT NULL,
  `last_used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `is_suspicious` BOOLEAN DEFAULT FALSE,
  `security_notes` TEXT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_user_sessions_user_id` (`user_id`),
  INDEX `idx_user_sessions_token_hash` (`token_hash`),
  INDEX `idx_user_sessions_expires_at` (`expires_at`),
  INDEX `idx_user_sessions_is_active` (`is_active`),
  INDEX `idx_user_sessions_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notifications table
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` VARCHAR(36) NOT NULL DEFAULT (UUID()),
  `user_id` VARCHAR(36) NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `type` ENUM('transfer_created', 'transfer_completed', 'transfer_failed', 'payment_received', 'kyc_approved', 'kyc_rejected', 'login_alert', 'security_alert', 'system_maintenance', 'promotional', 'general') NOT NULL,
  `sent_email` BOOLEAN DEFAULT FALSE,
  `sent_sms` BOOLEAN DEFAULT FALSE,
  `sent_push` BOOLEAN DEFAULT FALSE,
  `sent_in_app` BOOLEAN DEFAULT TRUE,
  `status` ENUM('pending', 'sent', 'delivered', 'failed', 'cancelled') DEFAULT 'pending',
  `priority` ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  `is_read` BOOLEAN DEFAULT FALSE,
  `read_at` TIMESTAMP NULL,
  `is_archived` BOOLEAN DEFAULT FALSE,
  `archived_at` TIMESTAMP NULL,
  `related_transfer_id` VARCHAR(36),
  `related_entity_type` VARCHAR(255),
  `related_entity_id` VARCHAR(255),
  `scheduled_at` TIMESTAMP NULL,
  `sent_at` TIMESTAMP NULL,
  `delivered_at` TIMESTAMP NULL,
  `retry_count` INT DEFAULT 0,
  `delivery_error` TEXT,
  `metadata` JSON,
  `template_data` JSON,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_notifications_user_id` (`user_id`),
  INDEX `idx_notifications_type` (`type`),
  INDEX `idx_notifications_status` (`status`),
  INDEX `idx_notifications_priority` (`priority`),
  INDEX `idx_notifications_is_read` (`is_read`),
  INDEX `idx_notifications_created_at` (`created_at`),
  INDEX `idx_notifications_scheduled_at` (`scheduled_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`related_transfer_id`) REFERENCES `transfers`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create knex_migrations table
CREATE TABLE IF NOT EXISTS `knex_migrations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255),
  `batch` INT,
  `migration_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert migration records
INSERT INTO `knex_migrations` (`name`, `batch`) VALUES
('001_create_users_table.js', 1),
('002_create_transfers_table.js', 1),
('003_create_sessions_table.js', 1),
('004_create_notifications_table.js', 1);

-- Insert sample users
INSERT INTO `users` (
  `id`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `status`, `kyc_status`,
  `is_email_verified`, `preferred_language`, `preferred_currency`
) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.',
  'System',
  'Administrator',
  'super_admin',
  'active',
  'approved',
  TRUE,
  'en',
  'USD'
),
(
  '550e8400-e29b-41d4-a716-************',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.',
  'Sample',
  'Agent',
  'agent',
  'active',
  'approved',
  TRUE,
  'en',
  'USD'
),
(
  '550e8400-e29b-41d4-a716-************',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.',
  'John',
  'Doe',
  'customer',
  'active',
  'approved',
  TRUE,
  'en',
  'USD'
);

-- Insert sample transfers
INSERT INTO `transfers` (
  `id`, `reference_number`, `tracking_number`, `sender_id`, `sender_name`, `sender_email`,
  `recipient_name`, `recipient_phone`, `recipient_country`, `send_amount`, `send_currency`,
  `receive_amount`, `receive_currency`, `exchange_rate`, `fees`, `total_amount`,
  `delivery_method`, `status`, `payment_status`, `completed_at`
) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  'WMT12345678',
  '123456789012',
  '550e8400-e29b-41d4-a716-************',
  'John Doe',
  '<EMAIL>',
  'Ahmed Ali',
  '+************',
  'SA',
  1000.00,
  'USD',
  3750.00,
  'SAR',
  3.750000,
  15.00,
  1015.00,
  'cash_pickup',
  'completed',
  'paid',
  DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
  '660e8400-e29b-41d4-a716-************',
  'WMT87654321',
  '************',
  '550e8400-e29b-41d4-a716-************',
  'John Doe',
  '<EMAIL>',
  'Fatima Hassan',
  '+************',
  'AE',
  500.00,
  'USD',
  1835.00,
  'AED',
  3.670000,
  8.00,
  508.00,
  'bank_deposit',
  'processing',
  'paid',
  NULL
);

-- Create exchange rates table for reference
CREATE TABLE IF NOT EXISTS `exchange_rates` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `base_currency` VARCHAR(3) NOT NULL DEFAULT 'USD',
  `target_currency` VARCHAR(3) NOT NULL,
  `rate` DECIMAL(10,6) NOT NULL,
  `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_currency_pair` (`base_currency`, `target_currency`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample exchange rates
INSERT INTO `exchange_rates` (`base_currency`, `target_currency`, `rate`) VALUES
('USD', 'SAR', 3.750000),
('USD', 'AED', 3.670000),
('USD', 'EGP', 30.850000),
('USD', 'JOD', 0.710000),
('USD', 'KWD', 0.310000),
('USD', 'QAR', 3.640000),
('USD', 'BHD', 0.380000),
('USD', 'OMR', 0.380000),
('USD', 'EUR', 0.850000),
('USD', 'GBP', 0.730000);

-- Success message
SELECT 'Database setup completed successfully! You can now use phpMyAdmin to manage your Wistron Money Transfer database.' as message;
