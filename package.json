{"name": "wistron-money-transfer", "version": "1.0.0", "description": "نظام تحويل الأموال المتكامل - Wistron Money Transfer System", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm run dev", "backend:install": "cd backend && npm install", "frontend:install": "cd frontend && npm install", "install:all": "npm run backend:install && npm run frontend:install", "build": "npm run backend:build && npm run frontend:build", "backend:build": "cd backend && npm run build", "frontend:build": "cd frontend && npm run build", "test": "npm run backend:test && npm run frontend:test", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "migrate": "cd backend && npm run migrate", "seed": "cd backend && npm run seed"}, "keywords": ["money-transfer", "fintech", "payment-system", "remittance", "financial-services"], "author": "Wistron Development Team", "license": "PROPRIETARY", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/wistron/money-transfer-system.git"}, "bugs": {"url": "https://github.com/wistron/money-transfer-system/issues"}, "homepage": "https://github.com/wistron/money-transfer-system#readme", "dependencies": {"bcrypt": "^6.0.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "pg": "^8.16.3"}}