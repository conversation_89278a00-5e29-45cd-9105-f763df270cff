/**
 * Advanced Authentication Service
 * خدمة المصادقة المتقدمة
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const dbManager = require('../database/connection');

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'wistron_jwt_secret_key_2024';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.bcryptRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.maxFailedAttempts = 5;
    this.lockoutDuration = 30 * 60 * 1000; // 30 minutes
  }

  /**
   * Hash password using bcrypt
   * تشفير كلمة المرور
   */
  async hashPassword(password) {
    try {
      const salt = await bcrypt.genSalt(this.bcryptRounds);
      return await bcrypt.hash(password, salt);
    } catch (error) {
      throw new Error('Password hashing failed');
    }
  }

  /**
   * Verify password against hash
   * التحقق من كلمة المرور
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate JWT token
   * إنشاء رمز JWT
   */
  generateToken(payload, expiresIn = this.jwtExpiresIn) {
    return jwt.sign(payload, this.jwtSecret, { 
      expiresIn,
      issuer: 'wistron-money-transfer',
      audience: 'wistron-users'
    });
  }

  /**
   * Verify JWT token
   * التحقق من رمز JWT
   */
  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret, {
        issuer: 'wistron-money-transfer',
        audience: 'wistron-users'
      });
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Generate refresh token
   * إنشاء رمز التحديث
   */
  generateRefreshToken() {
    return crypto.randomBytes(64).toString('hex');
  }

  /**
   * Authenticate user with email and password
   * مصادقة المستخدم بالبريد الإلكتروني وكلمة المرور
   */
  async authenticateUser(email, password, deviceInfo = {}) {
    const db = dbManager.getConnection();
    
    try {
      // Find user by email
      let user;
      if (db.isInMemory) {
        user = db.users.find(u => u.email === email);
      } else {
        user = await db('users').where({ email }).first();
      }

      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Check if account is locked
      if (user.locked_until && new Date(user.locked_until) > new Date()) {
        const lockTimeRemaining = Math.ceil((new Date(user.locked_until) - new Date()) / 60000);
        throw new Error(`Account locked. Try again in ${lockTimeRemaining} minutes`);
      }

      // Check if account is active
      if (user.status !== 'active') {
        throw new Error('Account is not active');
      }

      // Verify password
      const isPasswordValid = await this.verifyPassword(password, user.password_hash);
      
      if (!isPasswordValid) {
        await this.handleFailedLogin(user.id);
        throw new Error('Invalid credentials');
      }

      // Reset failed attempts on successful login
      await this.resetFailedAttempts(user.id);

      // Generate tokens
      const accessToken = this.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
        type: 'access'
      });

      const refreshToken = this.generateRefreshToken();

      // Create session record
      await this.createSession(user.id, accessToken, refreshToken, deviceInfo);

      // Update last login
      await this.updateLastLogin(user.id, deviceInfo.ip);

      // Return user data and tokens
      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          status: user.status,
          kycStatus: user.kyc_status,
          isEmailVerified: user.is_email_verified,
          isPhoneVerified: user.is_phone_verified,
          is2FAEnabled: user.is_2fa_enabled,
          preferredLanguage: user.preferred_language,
          preferredCurrency: user.preferred_currency
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: this.jwtExpiresIn
        }
      };

    } catch (error) {
      console.error('Authentication error:', error.message);
      throw error;
    }
  }

  /**
   * Handle failed login attempt
   * معالجة محاولة تسجيل دخول فاشلة
   */
  async handleFailedLogin(userId) {
    const db = dbManager.getConnection();
    
    try {
      if (db.isInMemory) {
        const user = db.users.find(u => u.id === userId);
        if (user) {
          user.failed_login_attempts = (user.failed_login_attempts || 0) + 1;
          
          if (user.failed_login_attempts >= this.maxFailedAttempts) {
            user.locked_until = new Date(Date.now() + this.lockoutDuration).toISOString();
          }
        }
      } else {
        const user = await db('users').where({ id: userId }).first();
        const failedAttempts = (user.failed_login_attempts || 0) + 1;
        
        const updateData = { failed_login_attempts: failedAttempts };
        
        if (failedAttempts >= this.maxFailedAttempts) {
          updateData.locked_until = new Date(Date.now() + this.lockoutDuration);
        }
        
        await db('users').where({ id: userId }).update(updateData);
      }
    } catch (error) {
      console.error('Failed to handle failed login:', error.message);
    }
  }

  /**
   * Reset failed login attempts
   * إعادة تعيين محاولات تسجيل الدخول الفاشلة
   */
  async resetFailedAttempts(userId) {
    const db = dbManager.getConnection();
    
    try {
      if (db.isInMemory) {
        const user = db.users.find(u => u.id === userId);
        if (user) {
          user.failed_login_attempts = 0;
          user.locked_until = null;
        }
      } else {
        await db('users').where({ id: userId }).update({
          failed_login_attempts: 0,
          locked_until: null
        });
      }
    } catch (error) {
      console.error('Failed to reset failed attempts:', error.message);
    }
  }

  /**
   * Create user session
   * إنشاء جلسة المستخدم
   */
  async createSession(userId, accessToken, refreshToken, deviceInfo) {
    const db = dbManager.getConnection();
    
    try {
      const tokenHash = crypto.createHash('sha256').update(accessToken).digest('hex');
      const refreshTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');
      
      const sessionData = {
        user_id: userId,
        token_hash: tokenHash,
        refresh_token_hash: refreshTokenHash,
        device_id: deviceInfo.deviceId,
        device_name: deviceInfo.deviceName,
        device_type: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        os: deviceInfo.os,
        ip_address: deviceInfo.ip,
        user_agent: deviceInfo.userAgent,
        location: deviceInfo.location,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        is_active: true
      };

      if (db.isInMemory) {
        db.user_sessions.push({
          id: String(db.user_sessions.length + 1),
          ...sessionData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      } else {
        await db('user_sessions').insert(sessionData);
      }
    } catch (error) {
      console.error('Failed to create session:', error.message);
    }
  }

  /**
   * Update last login information
   * تحديث معلومات آخر تسجيل دخول
   */
  async updateLastLogin(userId, ipAddress) {
    const db = dbManager.getConnection();
    
    try {
      const updateData = {
        last_login_at: new Date(),
        last_login_ip: ipAddress
      };

      if (db.isInMemory) {
        const user = db.users.find(u => u.id === userId);
        if (user) {
          Object.assign(user, updateData);
        }
      } else {
        await db('users').where({ id: userId }).update(updateData);
      }
    } catch (error) {
      console.error('Failed to update last login:', error.message);
    }
  }

  /**
   * Validate session token
   * التحقق من رمز الجلسة
   */
  async validateSession(token) {
    const db = dbManager.getConnection();
    
    try {
      // Verify JWT token
      const decoded = this.verifyToken(token);
      
      // Check session in database
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      
      let session;
      if (db.isInMemory) {
        session = db.user_sessions.find(s => 
          s.token_hash === tokenHash && 
          s.is_active && 
          new Date(s.expires_at) > new Date()
        );
      } else {
        session = await db('user_sessions')
          .where({ token_hash: tokenHash, is_active: true })
          .where('expires_at', '>', new Date())
          .first();
      }

      if (!session) {
        throw new Error('Session not found or expired');
      }

      // Update last used timestamp
      if (db.isInMemory) {
        session.last_used_at = new Date().toISOString();
      } else {
        await db('user_sessions')
          .where({ id: session.id })
          .update({ last_used_at: new Date() });
      }

      return decoded;
    } catch (error) {
      throw new Error('Invalid session');
    }
  }

  /**
   * Logout user (invalidate session)
   * تسجيل خروج المستخدم
   */
  async logout(token) {
    const db = dbManager.getConnection();
    
    try {
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      
      if (db.isInMemory) {
        const session = db.user_sessions.find(s => s.token_hash === tokenHash);
        if (session) {
          session.is_active = false;
        }
      } else {
        await db('user_sessions')
          .where({ token_hash: tokenHash })
          .update({ is_active: false });
      }
      
      return true;
    } catch (error) {
      console.error('Logout failed:', error.message);
      return false;
    }
  }
}

module.exports = new AuthService();
