# تعليمات إعداد قاعدة البيانات MySQL مع phpMyAdmin
# MySQL Database Setup Instructions with phpMyAdmin

## المتطلبات المسبقة | Prerequisites

1. **XAMPP** مثبت ويعمل على النظام
   - Apache Server running
   - MySQL Server running
   - phpMyAdmin accessible

2. **Node.js** (الإصدار 18 أو أحدث)

## خطوات الإعداد | Setup Steps

### 1. تشغيل XAMPP
- افتح XAMPP Control Panel
- تأكد من تشغيل Apache و MySQL
- تأكد من أن phpMyAdmin يعمل على: `http://localhost/phpmyadmin`

### 2. إنشاء قاعدة البيانات
#### الطريقة الأولى: استخدام phpMyAdmin
1. افتح phpMyAdmin في المتصفح: `http://localhost/phpmyadmin`
2. انقر على "New" لإنشاء قاعدة بيانات جديدة
3. اكتب اسم قاعدة البيانات: `wistron_money_transfer`
4. اختر Collation: `utf8mb4_unicode_ci`
5. انقر "Create"

#### الطريقة الثانية: استيراد ملف SQL
1. افتح phpMyAdmin
2. انقر على "Import" في القائمة العلوية
3. اختر ملف `database/wistron_mysql_setup.sql`
4. انقر "Go" لتنفيذ الاستيراد

### 3. تحديث إعدادات المشروع
تأكد من أن ملف `.env` يحتوي على الإعدادات الصحيحة:

```env
# Database Configuration (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wistron_money_transfer
DB_USER=root
DB_PASSWORD=
DB_SSL=false
```

### 4. تثبيت التبعيات
```bash
# تثبيت mysql2 driver
npm install mysql2

# إزالة PostgreSQL driver (إذا كان موجوداً)
npm uninstall pg
```

### 5. تشغيل المشروع
```bash
# تشغيل النظام الكامل
npm run dev

# أو تشغيل الخادم فقط
npm run backend:dev
```

## الجداول المُنشأة | Created Tables

1. **users** - جدول المستخدمين
2. **transfers** - جدول التحويلات
3. **user_sessions** - جدول جلسات المستخدمين
4. **notifications** - جدول الإشعارات
5. **exchange_rates** - جدول أسعار الصرف
6. **knex_migrations** - جدول تتبع الـ migrations

## البيانات التجريبية | Sample Data

### المستخدمون | Users
- **Admin**: <EMAIL> (كلمة المرور: Admin@123456)
- **Agent**: <EMAIL> (كلمة المرور: Agent@123456)
- **Customer**: <EMAIL> (كلمة المرور: Customer@123456)

### التحويلات | Transfers
- تحويل مكتمل من USD إلى SAR
- تحويل قيد المعالجة من USD إلى AED

## استكشاف الأخطاء | Troubleshooting

### خطأ الاتصال بقاعدة البيانات
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات `.env`
3. تأكد من وجود قاعدة البيانات `wistron_money_transfer`

### خطأ في الجداول
1. تأكد من استيراد ملف SQL بنجاح
2. تحقق من وجود جميع الجداول في phpMyAdmin
3. تأكد من صحة العلاقات بين الجداول

### خطأ في التبعيات
```bash
# إعادة تثبيت التبعيات
npm install

# تأكد من وجود mysql2
npm list mysql2
```

## الوصول إلى phpMyAdmin
- **URL**: http://localhost/phpmyadmin
- **Username**: root
- **Password**: (فارغ بشكل افتراضي)

## ملاحظات مهمة | Important Notes

1. **الأمان**: في بيئة الإنتاج، تأكد من تعيين كلمة مرور قوية لـ MySQL
2. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
3. **الأداء**: راقب أداء قاعدة البيانات وقم بتحسين الاستعلامات حسب الحاجة

## الدعم | Support
إذا واجهت أي مشاكل، تحقق من:
- سجلات XAMPP
- سجلات Node.js
- رسائل الخطأ في phpMyAdmin
