# ملخص تحويل قاعدة البيانات إلى MySQL
# MySQL Database Migration Summary

## التغييرات المُنجزة | Completed Changes

### 1. تحديث التبعيات | Dependencies Update
- ✅ إزالة `pg` (PostgreSQL driver)
- ✅ إضافة `mysql2` (MySQL driver)
- ✅ تحديث `package.json` الرئيسي
- ✅ تحديث `backend/package.json`

### 2. تحديث ملفات الإعدادات | Configuration Files Update
- ✅ تحديث `database/config.js`
- ✅ تحديث `backend/knexfile.js`
- ✅ تحديث ملف `.env`

### 3. تحديث ملفات الـ Migrations | Migration Files Update
- ✅ `001_create_users_table.js` - تحويل UUID إلى VARCHAR(36)
- ✅ `002_create_transfers_table.js` - تحديث المراجع والأنواع
- ✅ `003_create_sessions_table.js` - تحديث المراجع
- ✅ `004_create_notifications_table.js` - تحديث JSON types

### 4. إنشاء ملفات جديدة | New Files Created
- ✅ `database/wistron_mysql_setup.sql` - ملف SQL كامل للاستيراد
- ✅ `database/MYSQL_SETUP_INSTRUCTIONS.md` - تعليمات مفصلة
- ✅ `setup-mysql.bat` - script إعداد Windows
- ✅ `setup-mysql.sh` - script إعداد Linux/Mac
- ✅ `MYSQL_MIGRATION_SUMMARY.md` - هذا الملف

### 5. تحديث الوثائق | Documentation Update
- ✅ تحديث `README.md` مع تعليمات MySQL
- ✅ إضافة قسم إعداد phpMyAdmin

## التغييرات التقنية الرئيسية | Key Technical Changes

### من PostgreSQL إلى MySQL
| العنصر | PostgreSQL | MySQL |
|---------|------------|-------|
| Driver | `pg` | `mysql2` |
| Port | 5432 | 3306 |
| UUID | `gen_random_uuid()` | `UUID()` |
| JSON | `jsonb` | `json` |
| UUID Type | `uuid` | `VARCHAR(36)` |

### الجداول المُنشأة | Created Tables
1. **users** - المستخدمين (36 عمود)
2. **transfers** - التحويلات (54 عمود)
3. **user_sessions** - جلسات المستخدمين (20 عمود)
4. **notifications** - الإشعارات (26 عمود)
5. **exchange_rates** - أسعار الصرف (5 أعمدة)
6. **knex_migrations** - تتبع الـ migrations (4 أعمدة)

### البيانات التجريبية | Sample Data
- 3 مستخدمين (Admin, Agent, Customer)
- 2 تحويل تجريبي
- 10 أسعار صرف
- 4 سجلات migrations

## خطوات التشغيل | Running Steps

### 1. إعداد XAMPP
```bash
# تشغيل Apache + MySQL في XAMPP Control Panel
```

### 2. تثبيت التبعيات
```bash
# Windows
./setup-mysql.bat

# Linux/Mac
chmod +x setup-mysql.sh
./setup-mysql.sh
```

### 3. إعداد قاعدة البيانات
```bash
# فتح phpMyAdmin
http://localhost/phpmyadmin

# استيراد ملف SQL
database/wistron_mysql_setup.sql
```

### 4. تشغيل التطبيق
```bash
npm run dev
```

## الوصول للنظام | System Access

### URLs
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5000
- **phpMyAdmin**: http://localhost/phpmyadmin
- **API Docs**: http://localhost:5000/api-docs

### حسابات التجربة | Test Accounts
- **<EMAIL>** / Admin@123456
- **<EMAIL>** / Agent@123456  
- **<EMAIL>** / Customer@123456

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues
1. **خطأ الاتصال**: تأكد من تشغيل MySQL في XAMPP
2. **خطأ التبعيات**: تشغيل `npm install mysql2`
3. **خطأ قاعدة البيانات**: تأكد من استيراد ملف SQL
4. **خطأ المنافذ**: تأكد من عدم استخدام المنافذ 3000, 5000, 3306

### ملفات السجلات | Log Files
- XAMPP MySQL: `xampp/mysql/data/mysql_error.log`
- Node.js: Console output
- phpMyAdmin: Browser console

## الملاحظات المهمة | Important Notes

### الأمان | Security
- ⚠️ كلمة مرور MySQL فارغة في التطوير
- ⚠️ تأكد من تعيين كلمة مرور قوية في الإنتاج
- ⚠️ تفعيل SSL في بيئة الإنتاج

### الأداء | Performance
- 📊 مراقبة استعلامات قاعدة البيانات
- 📊 تحسين الفهارس حسب الحاجة
- 📊 عمل نسخ احتياطية دورية

### التطوير | Development
- 🔧 استخدام phpMyAdmin لإدارة البيانات
- 🔧 تشغيل migrations عند إضافة جداول جديدة
- 🔧 اختبار التطبيق مع بيانات حقيقية

---
**تم إنجاز التحويل بنجاح! 🎉**
