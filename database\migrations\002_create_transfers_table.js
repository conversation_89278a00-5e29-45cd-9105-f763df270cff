/**
 * Transfers Table Migration
 * إنشاء جدول التحويلات
 */

exports.up = function(knex) {
  return knex.schema.createTable('transfers', function(table) {
    // Primary key
    table.string('id', 36).primary().defaultTo(knex.raw('(UUID())'));

    // Transfer reference
    table.string('reference_number').unique().notNullable();
    table.string('tracking_number').unique().notNullable();

    // Sender information
    table.string('sender_id', 36).references('id').inTable('users').onDelete('RESTRICT');
    table.string('sender_name').notNullable();
    table.string('sender_phone');
    table.string('sender_email');
    table.string('sender_address');
    table.string('sender_city');
    table.string('sender_country', 2);
    
    // Recipient information
    table.string('recipient_name').notNullable();
    table.string('recipient_phone');
    table.string('recipient_email');
    table.string('recipient_address');
    table.string('recipient_city');
    table.string('recipient_country', 2).notNullable();
    table.string('recipient_id_type'); // passport, national_id, etc.
    table.string('recipient_id_number');
    
    // Transfer details
    table.decimal('send_amount', 15, 2).notNullable();
    table.string('send_currency', 3).notNullable();
    table.decimal('receive_amount', 15, 2).notNullable();
    table.string('receive_currency', 3).notNullable();
    table.decimal('exchange_rate', 10, 6).notNullable();
    table.decimal('fees', 15, 2).notNullable();
    table.decimal('total_amount', 15, 2).notNullable(); // send_amount + fees
    
    // Transfer method
    table.enum('delivery_method', [
      'cash_pickup',
      'bank_deposit',
      'mobile_wallet',
      'home_delivery'
    ]).notNullable();
    
    // Bank details (if bank_deposit)
    table.string('bank_name');
    table.string('bank_code');
    table.string('account_number');
    table.string('account_holder_name');
    table.string('swift_code');
    table.string('iban');
    
    // Mobile wallet details (if mobile_wallet)
    table.string('wallet_provider');
    table.string('wallet_number');
    
    // Pickup details (if cash_pickup)
    table.string('pickup_agent_id', 36).references('id').inTable('users');
    table.string('pickup_location');
    table.string('pickup_address');

    // Status and tracking
    table.enum('status', [
      'pending',
      'processing',
      'ready_for_pickup',
      'completed',
      'cancelled',
      'refunded',
      'failed'
    ]).defaultTo('pending');

    table.enum('payment_status', [
      'pending',
      'paid',
      'failed',
      'refunded'
    ]).defaultTo('pending');

    // Important dates
    table.timestamp('paid_at');
    table.timestamp('processed_at');
    table.timestamp('completed_at');
    table.timestamp('cancelled_at');
    table.timestamp('expires_at');

    // Purpose and compliance
    table.string('purpose_of_transfer');
    table.text('transfer_reason');
    table.boolean('is_suspicious').defaultTo(false);
    table.text('compliance_notes');

    // Agent information
    table.string('processing_agent_id', 36).references('id').inTable('users');
    table.string('completing_agent_id', 36).references('id').inTable('users');
    
    // Payment information
    table.string('payment_method'); // card, bank_transfer, cash, etc.
    table.string('payment_reference');
    table.json('payment_details').defaultTo('{}');

    // Metadata
    table.json('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['reference_number']);
    table.index(['tracking_number']);
    table.index(['sender_id']);
    table.index(['status']);
    table.index(['payment_status']);
    table.index(['delivery_method']);
    table.index(['recipient_country']);
    table.index(['created_at']);
    table.index(['expires_at']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('transfers');
};
