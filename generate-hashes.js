// Generate bcrypt hashes for passwords
const bcrypt = require('bcrypt');

async function generateHashes() {
  console.log('🔐 Generating bcrypt hashes...\n');
  
  const passwords = [
    { name: 'Admin', password: 'Admin@123456' },
    { name: 'Agent', password: 'Agent@123456' }
  ];
  
  for (const item of passwords) {
    const hash = await bcrypt.hash(item.password, 12);
    console.log(`${item.name} Password: ${item.password}`);
    console.log(`${item.name} Hash: ${hash}`);
    
    // Verify the hash works
    const isValid = await bcrypt.compare(item.password, hash);
    console.log(`${item.name} Verification: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    console.log('');
  }
}

generateHashes();
