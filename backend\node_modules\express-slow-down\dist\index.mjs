// source/slow-down.ts
import { rateLimit } from "express-rate-limit";
var filterUndefinedOptions = (passedOptions) => {
  const filteredOptions = {};
  for (const k of Object.keys(passedOptions)) {
    const key = k;
    if (passedOptions[key] !== void 0) {
      filteredOptions[key] = passedOptions[key];
    }
  }
  return filteredOptions;
};
var ExpressSlowDownWarning = class extends Error {
  constructor(code, message) {
    const url = `https://express-rate-limit.github.io/${code}/`;
    super(`${messa