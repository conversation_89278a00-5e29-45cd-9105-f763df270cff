/**
 * Create Notifications Table Migration
 * إنشاء جدول الإشعارات
 */

exports.up = function(knex) {
  return knex.schema.createTable('notifications', function(table) {
    // Primary key
    table.string('id', 36).primary().defaultTo(knex.raw('(UUID())'));

    // User reference
    table.string('user_id', 36).notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Notification content
    table.string('title').notNullable();
    table.text('message').notNullable();
    table.enum('type', [
      'transfer_created',
      'transfer_completed', 
      'transfer_failed',
      'payment_received',
      'kyc_approved',
      'kyc_rejected',
      'login_alert',
      'security_alert',
      'system_maintenance',
      'promotional',
      'general'
    ]).notNullable();
    
    // Notification channels
    table.boolean('sent_email').defaultTo(false);
    table.boolean('sent_sms').defaultTo(false);
    table.boolean('sent_push').defaultTo(false);
    table.boolean('sent_in_app').defaultTo(true);
    
    // Status and priority
    table.enum('status', ['pending', 'sent', 'delivered', 'failed', 'cancelled']).defaultTo('pending');
    table.enum('priority', ['low', 'normal', 'high', 'urgent']).defaultTo('normal');
    
    // User interaction
    table.boolean('is_read').defaultTo(false);
    table.timestamp('read_at').nullable();
    table.boolean('is_archived').defaultTo(false);
    table.timestamp('archived_at').nullable();
    
    // Related entities
    table.string('related_transfer_id', 36).nullable();
    table.foreign('related_transfer_id').references('id').inTable('transfers').onDelete('SET NULL');
    table.string('related_entity_type').nullable(); // transfer, payment, user, etc.
    table.string('related_entity_id').nullable();
    
    // Delivery tracking
    table.timestamp('scheduled_at').nullable();
    table.timestamp('sent_at').nullable();
    table.timestamp('delivered_at').nullable();
    table.integer('retry_count').defaultTo(0);
    table.text('delivery_error').nullable();
    
    // Metadata
    table.json('metadata').nullable();
    table.json('template_data').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['type']);
    table.index(['status']);
    table.index(['priority']);
    table.index(['is_read']);
    table.index(['created_at']);
    table.index(['scheduled_at']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('notifications');
};
