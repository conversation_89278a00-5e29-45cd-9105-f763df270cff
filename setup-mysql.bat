@echo off
echo ========================================
echo Wistron Money Transfer - MySQL Setup
echo ========================================
echo.

echo [1/4] Installing MySQL driver...
npm install mysql2
if %errorlevel% neq 0 (
    echo Error: Failed to install mysql2
    pause
    exit /b 1
)

echo [2/4] Removing PostgreSQL driver...
npm uninstall pg
echo PostgreSQL driver removed (if it was installed)

echo [3/4] Installing backend dependencies...
cd backend
npm install mysql2
if %errorlevel% neq 0 (
    echo Error: Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo [4/4] Setup completed successfully!
echo.
echo ========================================
echo Next Steps:
echo ========================================
echo 1. Make sure XAMPP is running (Apache + MySQL)
echo 2. Open phpMyAdmin: http://localhost/phpmyadmin
echo 3. Import the SQL file: database/wistron_mysql_setup.sql
echo 4. Run the application: npm run dev
echo.
echo For detailed instructions, see: database/MYSQL_SETUP_INSTRUCTIONS.md
echo ========================================
pause
